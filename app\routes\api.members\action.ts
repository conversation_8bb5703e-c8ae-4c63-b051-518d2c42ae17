import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import { ActionFunctionArgs } from "@remix-run/node";
import {
  mapErrors,
  mapJoiErrorToMemberError,
  mapToShopifyInput,
  memberSchema,
} from "./member.schema";
import { CREATE_CUSTOMER_MUTATION } from "./mutation";

export async function action({ request }: ActionFunctionArgs) {
  const { method } = request;

  if (method !== "POST") {
    const result = new Response(null, { status: 405 });
    console.log("⚠️ Members action result: Method not allowed -", `method: ${method}, status: 405`);
    return result;
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  try {
    await memberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    const result = responseBadRequest(mapJoiErrorToMemberError(error.details));
    console.log(
      "❌ Members action result: Validation error -",
      `errors: ${error.details?.length ?? 0} validation issues`,
    );
    return result;
  }

  const bodyData = mapToShopifyInput(body);

  const { data, errors } = await client.mutate({
    mutation: CREATE_CUSTOMER_MUTATION,
    variables: { input: bodyData },
    fetchPolicy: "no-cache",
  });

  if (errors) {
    console.log(
      "❌ Members action result: GraphQL errors -",
      `errors: ${errors?.length ?? 0} GraphQL issues, status: 500`,
    );
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  const { userErrors } = data.customerCreate;

  if (userErrors && userErrors.length > 0) {
    const result = responseBadRequest(mapErrors(userErrors));
    console.log(
      "❌ Members action result: User errors from Shopify -",
      `errors: ${userErrors?.length ?? 0} Shopify user errors`,
    );
    return result;
  }

  const result = new Response("", { status: 201 });
  console.log(
    "✅ Members action result: Success - Customer created successfully",
    `status: 201, customerId: ${data.customerCreate?.customer?.id ?? "unknown"}`,
  );
  return result;
}
