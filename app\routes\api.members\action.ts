import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import { ActionFunctionArgs } from "@remix-run/node";
import {
  mapErrors,
  mapJoiErrorToMemberError,
  mapToShopifyInput,
  memberSchema,
} from "./member.schema";
import { CREATE_CUSTOMER_MUTATION } from "./mutation";

export async function action({ request }: ActionFunctionArgs) {
  const { method } = request;

  if (method !== "POST") {
    return new Response(null, { status: 405 });
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  try {
    await memberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    return responseBadRequest(mapJoiErrorToMemberError(error.details));
  }

  const bodyData = mapToShopifyInput(body);

  const { data, errors } = await client.mutate({
    mutation: CREATE_CUSTOMER_MUTATION,
    variables: { input: bodyData },
    fetchPolicy: "no-cache",
  });

  if (errors) {
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  const { userErrors } = data.customerCreate;

  if (userErrors && userErrors.length > 0) {
    return responseBadRequest(mapErrors(userErrors));
  }

  return new Response("", { status: 201 });
}
