import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import { ActionFunctionArgs } from "@remix-run/node";
import {
  mapErrors,
  mapJoiErrorToMemberError,
  mapToShopifyInput,
  memberSchema,
} from "./member.schema";
import { CREATE_CUSTOMER_MUTATION } from "./mutation";

export async function action({ request }: ActionFunctionArgs) {
  const { method } = request;

  if (method !== "POST") {
    const result = new Response(null, { status: 405 });
    console.log(`⚠️ ${method} ${new URL(request.url).pathname} - Method not allowed (405)`);
    return result;
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  try {
    await memberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    const result = responseBadRequest(mapJoiErrorToMemberError(error.details));
    console.log(
      `❌ ${method} ${new URL(request.url).pathname} - Validation failed (${error.details?.length ?? 0} errors)`,
    );
    return result;
  }

  const bodyData = mapToShopifyInput(body);

  const { data, errors } = await client.mutate({
    mutation: CREATE_CUSTOMER_MUTATION,
    variables: { input: bodyData },
    fetchPolicy: "no-cache",
  });

  if (errors) {
    console.log(
      `❌ ${method} ${new URL(request.url).pathname} - GraphQL error (500, ${errors?.length ?? 0} issues)`,
    );
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  const { userErrors } = data.customerCreate;

  if (userErrors && userErrors.length > 0) {
    const result = responseBadRequest(mapErrors(userErrors));
    console.log(
      `❌ ${method} ${new URL(request.url).pathname} - Shopify user errors (${userErrors?.length ?? 0} errors)`,
    );
    return result;
  }

  const result = new Response("", { status: 201 });
  console.log(
    `✅ ${method} ${new URL(request.url).pathname} - Customer created (201, ID: ${data.customerCreate?.customer?.id ?? "unknown"})`,
  );
  return result;
}
