import { ActionFunctionArgs } from "@remix-run/node";
import dayjs from "dayjs";
import { CustomerStatus } from "../../constants";
import db from "../../db.server";
import { verifyToken } from "../../utils/auth.server";
import responseBadRequest from "../../utils/response.badRequest";
import responseSuccess from "../../utils/response.success";
import { updateCustomerMutation } from "../api.members.$id/mutation";
import { mapJoiErrorToMemberError } from "../api.members/member.schema";
import { MEMBER_QUERY, TAIWAN_COUNTRY_PHONE_CODE } from "../api.members/query";
import { cancelMemberSchema } from "./cancel-member.shema";
import { deleteCustomerMutation } from "./query";

export async function action({ request }: ActionFunctionArgs) {
  if (request.method !== "POST") {
    throw new Response(null, { status: 405 });
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  try {
    await cancelMemberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    return responseBadRequest(mapJoiErrorToMemberError(error.details));
  }

  const { data: memberQueryResult } = await client.query({
    query: MEMBER_QUERY,
    variables: {
      identifier: {
        phoneNumber: `${TAIWAN_COUNTRY_PHONE_CODE}${body.cellphone}`,
      },
    },
  });

  if (!memberQueryResult.customerByIdentifier) {
    return responseBadRequest([
      {
        field: "cellphone",
        code: "invalid_value",
        message: "Member not found. Please check your input and try again.",
      },
    ]);
  }

  const { data, errors } = await client.mutate({
    mutation: deleteCustomerMutation,
    variables: {
      id: memberQueryResult.customerByIdentifier.id,
    },
  });

  if (data?.customerDelete?.userErrors && data?.customerDelete?.userErrors?.length > 0) {
    if (
      data.customerDelete.userErrors[0].message !==
      "Customer can’t be deleted because they have associated orders"
    )
      return responseBadRequest({
        field: "cellphone",
        code: "error",
        message: data.customerDelete.userErrors[0].message,
      });
    else {
      const { errors } = await client.mutate({
        mutation: updateCustomerMutation,
        variables: {
          input: {
            id: memberQueryResult.customerByIdentifier.id,
            metafields: [
              {
                key: "status",
                type: "single_line_text_field",
                value: CustomerStatus.DELETED,
              },
            ],
          },
        },
      });

      if (errors) {
        throw new Response(JSON.stringify(errors), { status: 500 });
      }

      return responseSuccess({}, "The member has been successfully canceled.");
    }
  }

  if (errors) {
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  await db.membershipCancel.create({
    data: {
      cancelDate: dayjs(body.cancelDate).toDate(),
      customerId: memberQueryResult.customerByIdentifier.id,
      cellphone: body.cellphone,
    },
  });

  return responseSuccess({}, "The member has been successfully canceled.");
}
