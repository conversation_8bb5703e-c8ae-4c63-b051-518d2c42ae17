import { verifyToken } from "@/utils/auth.server";
import responseBadRequest from "@/utils/response.badRequest";
import responseSuccess from "@/utils/response.success";
import { LoaderFunctionArgs } from "@remix-run/node";
import Jo<PERSON> from "joi";
import { mapJoiErrorToMemberError, phoneSchema, transformCustomerData } from "./member.schema";
import { MEMBER_QUERY, TAIWAN_COUNTRY_PHONE_CODE } from "./query";

export async function loader({ request }: LoaderFunctionArgs) {
  const { client } = await verifyToken(request);

  const url = new URL(request.url);

  const cellphone = url.searchParams.get("cellphone");

  const { error } = Joi.object({
    cellphone: phoneSchema,
  }).validate({ cellphone });

  if (error) {
    return responseBadRequest(mapJoiErrorToMemberError(error.details));
  }

  const result = await client.query({
    query: MEMBER_QUERY,
    variables: {
      identifier: { phoneNumber: `${TAIWAN_COUNTRY_PHONE_CODE}${cellphone}` },
    },
  });

  if (result.data.customerByIdentifier) {
    return responseSuccess({
      ...transformCustomerData(result.data.customerByIdentifier),
      memberTier: "",
      memberTierStartDate: "",
      memberTierEndDate: "",
      pointRewardType: "",
      pointReward: "",
    });
  }

  return responseBadRequest([
    {
      field: "cellphone",
      code: "invalid_value",
      message: "Member not found. Please check your input and try again.",
    },
  ]);
}
