import type { ActionFunctionArgs } from "@remix-run/node";
import { verifyToken } from "../../utils/auth.server";
import responseBadRequest from "../../utils/response.badRequest";
import responseSuccess from "../../utils/response.success";
import {
  mapErrors,
  mapJoiErrorToMemberError,
  mapToShopifyInput,
  transformCustomerData,
} from "../api.members/member.schema";
import { MEMBER_QUERY } from "../api.members/query";
import { updateCustomerMutation, updateMarketingNotifyMutation } from "./mutation";
import { updateMemberSchema } from "./update-member.shema";

export async function action({ request, params }: ActionFunctionArgs) {
  if (request.method !== "PATCH") {
    throw new Response(null, { status: 405 });
  }

  const { client } = await verifyToken(request);

  const body = await request.json().catch((e) => {
    throw responseBadRequest([], e.message);
  });

  try {
    await updateMemberSchema.validateAsync(body, { abortEarly: false });
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
  } catch (error: any) {
    return responseBadRequest(mapJoiErrorToMemberError(error.details));
  }

  if (!params.id) {
    return responseBadRequest([
      {
        field: "memberId",
        code: "invalid_value",
        message: "The 'memberId' must be a valid Shopify customer ID",
      },
    ]);
  }

  const customerId = params.id.includes("gid://shopify/Customer/")
    ? params.id
    : `gid://shopify/Customer/${params.id}`;

  const { data: memberQueryResult } = await client.query({
    query: MEMBER_QUERY,
    variables: {
      identifier: { id: customerId },
    },
  });

  if (!memberQueryResult.customerByIdentifier) {
    return responseBadRequest([
      {
        field: "memberId",
        code: "invalid_value",
        message: "Member not found",
      },
    ]);
  }

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const bodyData: Record<string, any> = {
    ...mapToShopifyInput(body),
    id: memberQueryResult.customerByIdentifier.id,
  };

  Object.keys(bodyData).forEach((key) => {
    if (
      bodyData[key] === "" ||
      bodyData[key] === undefined ||
      bodyData[key] === null ||
      (Array.isArray(bodyData[key]) && bodyData[key].length === 0)
    ) {
      delete bodyData[key];
    }
  });

  delete bodyData.emailMarketingConsent;

  const { data, errors } = await client.mutate({
    mutation: updateCustomerMutation,
    variables: { input: bodyData },
    fetchPolicy: "no-cache",
  });

  if (errors) {
    throw new Response(JSON.stringify(errors), { status: 500 });
  }

  const { userErrors, customer } = data.customerUpdate;

  if (userErrors && userErrors.length > 0) {
    return responseBadRequest(mapErrors(userErrors));
  }

  if (body.marketingNotify !== undefined) {
    const bodyData: {
      customerId: string;
      emailMarketingConsent: Record<string, string>;
    } = {
      customerId: memberQueryResult.customerByIdentifier.id,
      emailMarketingConsent: {},
    };

    if (body.marketingNotify) {
      bodyData.emailMarketingConsent = {
        marketingState: "SUBSCRIBED",
        marketingOptInLevel: "UNKNOWN",
      };
    } else {
      bodyData.emailMarketingConsent = {
        marketingState: "UNSUBSCRIBED",
      };
    }

    const { data, errors } = await client.mutate({
      mutation: updateMarketingNotifyMutation,
      variables: { input: bodyData },
      fetchPolicy: "no-cache",
    });

    if (errors) {
      throw new Response(JSON.stringify(errors), { status: 500 });
    }
    const { userErrors } = data.customerEmailMarketingConsentUpdate;

    if (userErrors && userErrors.length > 0) {
      return responseBadRequest(mapErrors(userErrors));
    }
  }

  const transformData = transformCustomerData(customer);

  return responseSuccess({
    ...transformData,
    marketingNotify:
      body.marketingNotify !== undefined ? body.marketingNotify : transformData.marketingNotify,
  });
}
